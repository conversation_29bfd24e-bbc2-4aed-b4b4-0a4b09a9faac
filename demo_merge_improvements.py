#!/usr/bin/env python3
"""
Demonstration of the Enhanced Entity Merge Logic

This script demonstrates the improvements made to the merge logic
for combining multiple AI extraction results.
"""

import json
from app.Extractor.services.advanced_entity_merger import AdvancedEntityMerger


def demo_basic_merging():
    """Demonstrate basic merging capabilities"""
    print("=== BASIC MERGING DEMONSTRATION ===\n")
    
    # Sample AI extraction results
    results = [
        {
            "data": {
                "legal_name": "TechCorp Solutions Pvt Ltd",
                "business_email": ["<EMAIL>"],
                "business_contact_numbers": ["+91-9876543210"],
                "business_location": ["Mumbai, Maharashtra, India"],
                "accepts_international_orders": "yes",
                "has_jurisdiction_law": "yes",
                "jurisdiction_place": ["India"],
                "shipping_countries": ["India", "USA"]
            },
            "source": "gemini",
            "source_index": 0
        },
        {
            "data": {
                "legal_name": "TECHCORP SOLUTIONS PVT LTD",  # Similar but different case
                "business_email": ["<EMAIL>", "<EMAIL>"],  # Overlap + new
                "support_email": ["<EMAIL>"],
                "business_contact_numbers": ["+91-9876543210", "+91-1234567890"],  # Overlap + new
                "business_location": ["Mumbai, India", "Delhi, India"],  # Similar + new
                "accepts_international_orders": "no",  # Conflict
                "has_jurisdiction_law": "no",  # Conflict
                "jurisdiction_place": ["India", "Maharashtra"],  # Overlap + new
                "shipping_countries": ["India", "Canada", "UK"]  # Overlap + new
            },
            "source": "openai",
            "source_index": 0
        },
        {
            "data": {
                "legal_name": "TechCorp Solutions",  # Shorter version
                "business_email": ["<EMAIL>"],  # New email
                "business_contact_numbers": ["not_available"],  # Garbage value
                "business_location": ["n/a"],  # Garbage value
                "accepts_international_orders": "yes",  # Supports first result
                "has_jurisdiction_law": "yes",  # Supports first result
                "jurisdiction_place": ["India", "Delhi"],
                "shipping_policy_details": "Ships worldwide with 5-7 business days delivery"
            },
            "source": "openai",
            "source_index": 1
        }
    ]
    
    # Create merger and process results
    merger = AdvancedEntityMerger()
    merged_result = merger.merge_multiple_results(results)
    
    print("Input Results:")
    for i, result in enumerate(results):
        print(f"\n{result['source'].upper()} Result {result['source_index']}:")
        print(json.dumps(result['data'], indent=2))
    
    print("\n" + "="*60)
    print("MERGED RESULT:")
    print("="*60)
    print(json.dumps(merged_result, indent=2))
    
    return merged_result


def demo_confidence_scoring():
    """Demonstrate confidence scoring"""
    print("\n\n=== CONFIDENCE SCORING DEMONSTRATION ===\n")
    
    merger = AdvancedEntityMerger()
    
    # Test different field types and quality
    test_cases = [
        ("business_email", ["<EMAIL>"], "High quality email"),
        ("business_email", ["invalid-email"], "Low quality email"),
        ("business_contact_numbers", ["+91-9876543210"], "Valid phone number"),
        ("business_contact_numbers", ["invalid-phone"], "Invalid phone number"),
        ("legal_name", "Professional Business Solutions Ltd", "Good business name"),
        ("legal_name", "abc", "Poor business name"),
        ("has_jurisdiction_law", "yes", "Clear boolean value"),
        ("has_jurisdiction_law", "unclear", "Unclear boolean value"),
    ]
    
    print("Confidence Scoring Examples:")
    print("-" * 50)
    
    for field, value, description in test_cases:
        confidence = merger._calculate_confidence(field, value, "gemini")
        print(f"{description:.<35} {confidence:.2f}")


def demo_garbage_detection():
    """Demonstrate enhanced garbage detection"""
    print("\n\n=== GARBAGE DETECTION DEMONSTRATION ===\n")
    
    merger = AdvancedEntityMerger()
    
    # Test various garbage patterns
    test_values = [
        ("Valid Company Name", False),
        ("not_applicable", True),
        ("N/A", True),
        ("information not available", True),
        ("", True),
        ("   ", True),
        (None, True),
        ([], True),
        (["<EMAIL>"], False),
        (["", "n/a", None], True),
        ("cannot extract", True),
        ("extraction failed", True),
        ("no information", True),
        ("details not provided", True),
        ("------------------", True),  # Only punctuation
        ("Valid Business Email", False),
    ]
    
    print("Garbage Detection Examples:")
    print("-" * 50)
    
    for value, expected_garbage in test_values:
        is_garbage = merger._is_garbage_value(value)
        status = "✓" if is_garbage == expected_garbage else "✗"
        value_display = repr(value) if len(str(value)) < 30 else f"{str(value)[:27]}..."
        print(f"{status} {value_display:.<35} {'GARBAGE' if is_garbage else 'VALID'}")


def demo_conflict_resolution():
    """Demonstrate conflict resolution strategies"""
    print("\n\n=== CONFLICT RESOLUTION DEMONSTRATION ===\n")
    
    merger = AdvancedEntityMerger()
    
    # Test boolean field conflict resolution
    print("Boolean Field Conflict Resolution:")
    print("-" * 40)
    
    # Case 1: Jurisdiction law - "yes" should win even with lower confidence
    jurisdiction_values = [
        {"value": "no", "confidence": 0.8, "source": "gemini"},
        {"value": "yes", "confidence": 0.6, "source": "openai"}
    ]
    
    result = merger._merge_boolean_field("has_jurisdiction_law", jurisdiction_values)
    print(f"Jurisdiction Law (yes vs no): {result} (yes should win)")
    
    # Case 2: International orders - highest weighted vote should win
    shipping_values = [
        {"value": "no", "confidence": 0.9, "source": "gemini"},
        {"value": "no", "confidence": 0.8, "source": "openai"},
        {"value": "yes", "confidence": 0.3, "source": "openai"}
    ]
    
    result = merger._merge_boolean_field("accepts_international_orders", shipping_values)
    print(f"International Orders (weighted voting): {result} (no should win)")
    
    # Test list field merging
    print("\nList Field Merging:")
    print("-" * 20)
    
    email_values = [
        {
            "value": ["<EMAIL>", "<EMAIL>"],
            "confidence": 0.9,
            "source": "gemini"
        },
        {
            "value": ["<EMAIL>", "<EMAIL>"],
            "confidence": 0.7,
            "source": "openai"
        }
    ]
    
    merged_emails = merger._merge_list_field("business_email", email_values)
    print(f"Merged emails: {merged_emails}")
    print("(Should contain all unique emails, ordered by confidence)")


def main():
    """Run all demonstrations"""
    print("ENHANCED ENTITY MERGE LOGIC DEMONSTRATION")
    print("=" * 60)
    
    try:
        demo_basic_merging()
        demo_confidence_scoring()
        demo_garbage_detection()
        demo_conflict_resolution()
        
        print("\n\n" + "=" * 60)
        print("DEMONSTRATION COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        print("\nKey Improvements:")
        print("• Advanced confidence scoring based on data quality")
        print("• Intelligent conflict resolution strategies")
        print("• Enhanced garbage detection with pattern matching")
        print("• Smart list merging with deduplication")
        print("• Field-specific validation and merging logic")
        print("• Support for multiple AI model outputs")
        
    except Exception as e:
        print(f"\nError during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
