"""
Test cases for the Advanced Entity Merger

This module contains comprehensive tests for the enhanced merge logic
that combines multiple AI extraction results.
"""

import pytest
from app.Extractor.services.advanced_entity_merger import AdvancedEntityMerger


class TestAdvancedEntityMerger:
    """Test cases for AdvancedEntityMerger"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.merger = AdvancedEntityMerger()
    
    def test_merge_multiple_results_basic(self):
        """Test basic merging of multiple AI results"""
        results = [
            {
                "data": {
                    "legal_name": "Acme Corporation Ltd",
                    "business_email": ["<EMAIL>"],
                    "has_jurisdiction_law": "yes"
                },
                "source": "gemini",
                "source_index": 0
            },
            {
                "data": {
                    "legal_name": "ACME CORP LTD",
                    "business_email": ["<EMAIL>", "<EMAIL>"],
                    "support_email": ["<EMAIL>"],
                    "has_jurisdiction_law": "no"
                },
                "source": "openai",
                "source_index": 0
            }
        ]
        
        merged = self.merger.merge_multiple_results(results)
        
        # Check that emails are properly merged and deduplicated
        assert "<EMAIL>" in merged["business_email"]
        assert "<EMAIL>" in merged["business_email"]
        assert len(merged["business_email"]) == 2
        
        # Check that support email is included
        assert merged["support_email"] == ["<EMAIL>"]
        
        # Check that jurisdiction "yes" wins over "no"
        assert merged["has_jurisdiction_law"] == "yes"
        
        # Check that legal name is properly selected
        assert merged["legal_name"] in ["Acme Corporation Ltd", "ACME CORP LTD"]
    
    def test_confidence_scoring(self):
        """Test confidence scoring for different field types"""
        # Test email confidence
        email_confidence = self.merger._calculate_confidence(
            "business_email", ["<EMAIL>"], "gemini"
        )
        assert email_confidence > 0.5
        
        invalid_email_confidence = self.merger._calculate_confidence(
            "business_email", ["invalid-email"], "gemini"
        )
        assert invalid_email_confidence < email_confidence
        
        # Test phone confidence
        phone_confidence = self.merger._calculate_confidence(
            "business_contact_numbers", ["******-567-8900"], "openai"
        )
        assert phone_confidence > 0.5
        
        # Test boolean confidence
        bool_confidence = self.merger._calculate_confidence(
            "has_jurisdiction_law", "yes", "gemini"
        )
        assert bool_confidence == 1.0
    
    def test_garbage_value_detection(self):
        """Test enhanced garbage value detection"""
        # Test obvious garbage values
        assert self.merger._is_garbage_value(None)
        assert self.merger._is_garbage_value("")
        assert self.merger._is_garbage_value("   ")
        assert self.merger._is_garbage_value("not_applicable")
        assert self.merger._is_garbage_value("N/A")
        assert self.merger._is_garbage_value("not found")
        assert self.merger._is_garbage_value("information not available")
        
        # Test valid values
        assert not self.merger._is_garbage_value("Acme Corp")
        assert not self.merger._is_garbage_value(["<EMAIL>"])
        assert not self.merger._is_garbage_value("yes")
        
        # Test empty lists
        assert self.merger._is_garbage_value([])
        assert self.merger._is_garbage_value(["", None, "n/a"])
        
        # Test valid lists
        assert not self.merger._is_garbage_value(["<EMAIL>"])
    
    def test_list_field_merging(self):
        """Test intelligent list field merging"""
        value_list = [
            {
                "value": ["<EMAIL>", "<EMAIL>"],
                "source": "gemini",
                "source_index": 0,
                "confidence": 0.9
            },
            {
                "value": ["<EMAIL>", "<EMAIL>"],
                "source": "openai",
                "source_index": 0,
                "confidence": 0.8
            }
        ]
        
        merged = self.merger._merge_list_field("business_email", value_list)
        
        # Should contain all unique emails
        assert len(merged) == 3
        assert "<EMAIL>" in merged
        assert "<EMAIL>" in merged
        assert "<EMAIL>" in merged
        
        # Should be ordered by confidence (highest first)
        assert merged[0] in ["<EMAIL>", "<EMAIL>"]  # From highest confidence source
    
    def test_boolean_field_merging(self):
        """Test boolean field merging with weighted voting"""
        # Test case where "yes" should win
        value_list = [
            {"value": "yes", "confidence": 0.8, "source": "gemini"},
            {"value": "no", "confidence": 0.7, "source": "openai"}
        ]
        
        result = self.merger._merge_boolean_field("has_jurisdiction_law", value_list)
        assert result == "yes"
        
        # Test case where "no" should win due to higher total confidence
        value_list = [
            {"value": "no", "confidence": 0.9, "source": "gemini"},
            {"value": "no", "confidence": 0.8, "source": "openai"},
            {"value": "yes", "confidence": 0.3, "source": "openai"}
        ]
        
        result = self.merger._merge_boolean_field("accepts_international_orders", value_list)
        assert result == "no"
    
    def test_string_field_merging(self):
        """Test string field merging"""
        value_list = [
            {"value": "Acme Corporation", "confidence": 0.9, "source": "gemini"},
            {"value": "ACME CORP", "confidence": 0.7, "source": "openai"}
        ]
        
        result = self.merger._merge_string_field("legal_name", value_list)
        assert result == "Acme Corporation"  # Higher confidence should win
    
    def test_legal_name_similarity_merging(self):
        """Test legal name merging with similarity checking"""
        value_list = [
            {"value": "Acme Corporation Ltd", "confidence": 0.8},
            {"value": "ACME CORPORATION LTD", "confidence": 0.9}  # Similar but higher confidence
        ]
        
        result = self.merger._merge_legal_name(value_list)
        assert result == "ACME CORPORATION LTD"  # Should pick higher confidence similar name
    
    def test_email_validation(self):
        """Test email validation quality scoring"""
        # Valid email
        quality = self.merger._validate_email_quality("<EMAIL>")
        assert quality == 1.0
        
        # Invalid email
        quality = self.merger._validate_email_quality("invalid-email")
        assert quality == 0.3
        
        # List with mixed validity
        quality = self.merger._validate_email_quality(["<EMAIL>", "invalid"])
        assert quality == 0.5  # 1 valid out of 2
    
    def test_phone_validation(self):
        """Test phone number validation quality scoring"""
        # Valid phone
        quality = self.merger._validate_phone_quality("******-567-8900")
        assert quality == 1.0
        
        # Invalid phone
        quality = self.merger._validate_phone_quality("invalid-phone")
        assert quality == 0.3
    
    def test_name_validation(self):
        """Test business name validation quality scoring"""
        # Good business name
        quality = self.merger._validate_name_quality("Acme Corporation Ltd")
        assert quality > 0.5
        
        # Poor business name
        quality = self.merger._validate_name_quality("a")
        assert quality <= 0.5
    
    def test_empty_results(self):
        """Test handling of empty results"""
        merged = self.merger.merge_multiple_results([])
        assert merged == {}
    
    def test_error_handling(self):
        """Test handling of results with errors"""
        results = [
            {
                "data": {"error": "API failed"},
                "source": "gemini",
                "source_index": 0
            },
            {
                "data": {
                    "legal_name": "Valid Corp",
                    "business_email": ["<EMAIL>"]
                },
                "source": "openai",
                "source_index": 0
            }
        ]
        
        merged = self.merger.merge_multiple_results(results)
        
        # Should only contain valid data, no error field
        assert "error" not in merged
        assert merged["legal_name"] == "Valid Corp"
        assert merged["business_email"] == ["<EMAIL>"]


if __name__ == "__main__":
    pytest.main([__file__])
