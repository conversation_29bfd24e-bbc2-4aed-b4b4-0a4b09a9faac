"""
Advanced Entity Merger for combining multiple AI extraction results

This module provides sophisticated merging logic with confidence scoring,
conflict resolution, and data validation for entity extraction results.
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from collections import defaultdict, Counter
from difflib import SequenceMatcher


class AdvancedEntityMerger:
    """
    Advanced merger for combining multiple AI extraction results with confidence scoring
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        
        # Field configuration
        self.list_fields = {
            "business_email", "support_email", "business_contact_numbers", 
            "business_location", "shipping_countries", "jurisdiction_place"
        }
        
        self.boolean_fields = {
            "accepts_international_orders", "has_jurisdiction_law"
        }
        
        self.string_fields = {
            "legal_name", "shipping_policy_details", "jurisdiction_details"
        }
        
        # Confidence weights by AI source
        self.source_weights = {
            "gemini": 1.0,
            "openai": 1.0
        }
        
        # Quality indicators for confidence scoring
        self.quality_indicators = {
            "email_pattern": re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
            "phone_pattern": re.compile(r'^[\+]?[1-9][\d\s\-\(\)]{7,15}$'),
            "url_pattern": re.compile(r'^https?://[^\s]+$'),
        }

    def merge_multiple_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Merge multiple AI extraction results using advanced strategies
        
        Args:
            results: List of results with format:
                    [{"data": {...}, "source": "gemini|openai", "source_index": int}]
        
        Returns:
            Dict containing merged results
        """
        if not results:
            return {}
        
        self.logger.info(f"Merging {len(results)} AI extraction results")
        
        # Group results by field
        field_values = defaultdict(list)
        
        for result in results:
            data = result.get("data", {})
            source = result.get("source", "unknown")
            source_index = result.get("source_index", 0)
            
            for field, value in data.items():
                if field == "error":
                    continue
                    
                # Calculate confidence score for this value
                confidence = self._calculate_confidence(field, value, source)
                
                field_values[field].append({
                    "value": value,
                    "source": source,
                    "source_index": source_index,
                    "confidence": confidence
                })
        
        # Merge each field using appropriate strategy
        merged_result = {}
        for field, value_list in field_values.items():
            merged_value = self._merge_field_values(field, value_list)
            if merged_value is not None:
                merged_result[field] = merged_value
        
        self.logger.info(f"Merged result contains {len(merged_result)} fields")
        return merged_result

    def _calculate_confidence(self, field: str, value: Any, source: str) -> float:
        """
        Calculate confidence score for a field value
        
        Returns:
            Float between 0.0 and 1.0
        """
        if self._is_garbage_value(value):
            return 0.0
        
        confidence = self.source_weights.get(source, 0.5)
        
        # Adjust confidence based on data quality indicators
        if field in ["business_email", "support_email"]:
            confidence *= self._validate_email_quality(value)
        elif field == "business_contact_numbers":
            confidence *= self._validate_phone_quality(value)
        elif field == "legal_name":
            confidence *= self._validate_name_quality(value)
        elif field in self.boolean_fields:
            confidence *= self._validate_boolean_quality(value)
        
        return min(confidence, 1.0)

    def _merge_field_values(self, field: str, value_list: List[Dict[str, Any]]) -> Any:
        """
        Merge values for a specific field using appropriate strategy
        """
        if not value_list:
            return None
        
        # Filter out garbage values
        valid_values = [v for v in value_list if v["confidence"] > 0.0]
        
        if not valid_values:
            return None
        
        # Sort by confidence (highest first)
        valid_values.sort(key=lambda x: x["confidence"], reverse=True)
        
        if field in self.list_fields:
            return self._merge_list_field(field, valid_values)
        elif field in self.boolean_fields:
            return self._merge_boolean_field(field, valid_values)
        elif field in self.string_fields:
            return self._merge_string_field(field, valid_values)
        else:
            # Default: return highest confidence value
            return valid_values[0]["value"]

    def _merge_list_field(self, field: str, value_list: List[Dict[str, Any]]) -> List[str]:
        """
        Merge list fields with intelligent deduplication
        """
        all_items = []
        seen_items = set()
        
        for value_data in value_list:
            value = value_data["value"]
            confidence = value_data["confidence"]
            
            if isinstance(value, list):
                for item in value:
                    if item and str(item).strip():
                        normalized_item = str(item).strip().lower()
                        if normalized_item not in seen_items:
                            all_items.append({
                                "item": str(item).strip(),
                                "confidence": confidence,
                                "normalized": normalized_item
                            })
                            seen_items.add(normalized_item)
        
        # Sort by confidence and return items
        all_items.sort(key=lambda x: x["confidence"], reverse=True)
        return [item["item"] for item in all_items]

    def _merge_boolean_field(self, field: str, value_list: List[Dict[str, Any]]) -> str:
        """
        Merge boolean fields using weighted voting
        """
        votes = {"yes": 0.0, "no": 0.0, "not_mentioned": 0.0}
        
        for value_data in value_list:
            value = str(value_data["value"]).lower().strip()
            confidence = value_data["confidence"]
            
            if value in ["yes", "true", "1"]:
                votes["yes"] += confidence
            elif value in ["no", "false", "0"]:
                votes["no"] += confidence
            else:
                votes["not_mentioned"] += confidence
        
        # Special case for jurisdiction: "yes" should win if there's any confident "yes"
        if field == "has_jurisdiction_law" and votes["yes"] > 0.3:
            return "yes"
        
        # Return the option with highest weighted vote
        return max(votes.items(), key=lambda x: x[1])[0]

    def _merge_string_field(self, field: str, value_list: List[Dict[str, Any]]) -> str:
        """
        Merge string fields using confidence-based selection or combination
        """
        # For most string fields, return the highest confidence value
        if field == "legal_name":
            return self._merge_legal_name(value_list)
        
        # Default: return highest confidence non-empty value
        for value_data in value_list:
            value = value_data["value"]
            if value and str(value).strip():
                return str(value).strip()
        
        return ""

    def _merge_legal_name(self, value_list: List[Dict[str, Any]]) -> str:
        """
        Merge legal name with similarity checking
        """
        names = []
        for value_data in value_list:
            value = value_data["value"]
            confidence = value_data["confidence"]
            if value and str(value).strip():
                names.append({
                    "name": str(value).strip(),
                    "confidence": confidence
                })

        if not names:
            return ""

        # If only one name, return it
        if len(names) == 1:
            return names[0]["name"]

        # Check for similar names and merge
        best_name = names[0]["name"]
        best_confidence = names[0]["confidence"]

        for name_data in names[1:]:
            name = name_data["name"]
            confidence = name_data["confidence"]

            # Check similarity
            similarity = SequenceMatcher(None, best_name.lower(), name.lower()).ratio()

            if similarity > 0.8:  # Very similar names
                # Keep the one with higher confidence
                if confidence > best_confidence:
                    best_name = name
                    best_confidence = confidence
            elif confidence > best_confidence * 1.5:  # Much higher confidence
                best_name = name
                best_confidence = confidence

        return best_name

    def _validate_email_quality(self, value: Any) -> float:
        """
        Validate email quality and return quality multiplier
        """
        if not value:
            return 0.0

        if isinstance(value, list):
            if not value:
                return 0.0
            # Check quality of all emails in list
            valid_count = 0
            for email in value:
                if email and self.quality_indicators["email_pattern"].match(str(email)):
                    valid_count += 1
            return valid_count / len(value)

        if isinstance(value, str) and self.quality_indicators["email_pattern"].match(value):
            return 1.0

        return 0.3  # Partial credit for non-matching but non-empty values

    def _validate_phone_quality(self, value: Any) -> float:
        """
        Validate phone number quality and return quality multiplier
        """
        if not value:
            return 0.0

        if isinstance(value, list):
            if not value:
                return 0.0
            valid_count = 0
            for phone in value:
                if phone and self.quality_indicators["phone_pattern"].match(str(phone).strip()):
                    valid_count += 1
            return valid_count / len(value) if value else 0.0

        if isinstance(value, str):
            cleaned_phone = re.sub(r'[^\d\+\-\(\)\s]', '', value.strip())
            if self.quality_indicators["phone_pattern"].match(cleaned_phone):
                return 1.0

        return 0.3  # Partial credit for non-matching but non-empty values

    def _validate_name_quality(self, value: Any) -> float:
        """
        Validate business name quality
        """
        if not value or not str(value).strip():
            return 0.0

        name = str(value).strip()

        # Check for common quality indicators
        quality_score = 0.5  # Base score

        # Longer names are generally better (up to a point)
        if 10 <= len(name) <= 100:
            quality_score += 0.2
        elif len(name) > 100:
            quality_score -= 0.1

        # Names with proper capitalization are better
        if name.istitle() or any(c.isupper() for c in name):
            quality_score += 0.2

        # Names with business indicators are better
        business_indicators = ["ltd", "llc", "inc", "corp", "company", "pvt", "private", "limited"]
        if any(indicator in name.lower() for indicator in business_indicators):
            quality_score += 0.1

        return min(quality_score, 1.0)

    def _validate_boolean_quality(self, value: Any) -> float:
        """
        Validate boolean field quality
        """
        if value is None:
            return 0.0

        value_str = str(value).lower().strip()

        # Clear boolean values get full score
        if value_str in ["yes", "no", "true", "false", "1", "0"]:
            return 1.0

        # Unclear values get lower score
        if value_str in ["not_mentioned", "unclear", "unknown"]:
            return 0.5

        return 0.3

    def _is_garbage_value(self, value: Any) -> bool:
        """
        Enhanced garbage value detection with better pattern matching
        """
        if value is None:
            return True

        if isinstance(value, str):
            # Empty or whitespace-only strings
            if not value.strip():
                return True

            # Enhanced garbage patterns
            garbage_patterns = [
                r'^(not?[_\s]?(applicable|available|found|specified|mentioned|provided|stated|given|disclosed|indicated|clear))',
                r'^(n/?a|na|none|null|undefined|unknown|unclear)$',
                r'^(information[_\s]?not[_\s]?available)',
                r'^(details[_\s]?not[_\s]?provided)',
                r'^(not[_\s]?extractable|cannot[_\s]?extract|unable[_\s]?to[_\s]?extract)',
                r'^(extraction[_\s]?failed|no[_\s]?extraction[_\s]?possible)',
                r'^(no[_\s]?(information|data|details|mention))',
                r'^[\-\*\.\s]+$',  # Only punctuation and spaces
            ]

            cleaned_value = value.lower().strip()
            for pattern in garbage_patterns:
                if re.match(pattern, cleaned_value):
                    return True

            return False

        if isinstance(value, list):
            # Empty lists are garbage
            if not value:
                return True
            # Lists containing only garbage strings are garbage
            return all(self._is_garbage_value(item) for item in value)

        if isinstance(value, dict):
            # Empty dicts are garbage
            if not value:
                return True
            # Dicts with only garbage values are garbage
            return all(self._is_garbage_value(v) for v in value.values())

        # For other types (int, float, bool), only None is garbage
        return False
